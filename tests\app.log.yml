- time: 2025.06.15-kl.14.13
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 742
  message: 'Hash file written: .original_hashes.py'

- time: 2025.06.15-kl.14.13
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 742
  message: 'Hash file written: .new_hashes.py'

- time: 2025.06.15-kl.14.13
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1370
  message: Opening new hash file for editing...

- time: 2025.06.15-kl.15.12
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1457
  message: 'Cleaned up: .original_hashes.py'

- time: 2025.06.15-kl.15.12
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1457
  message: 'Cleaned up: .new_hashes.py'

- time: 2025.06.15-kl.15.12
  level: !ERROR
  name: __main__
  funcName: *run
  lineno: 1366
  message: 'Execution failed: Can't get local object 'FileProcessor.collect_file_hashes.<locals>.process_file''

