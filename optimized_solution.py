"""
Optimized implementation for the FileHasher class that:
1. Uses xxhash instead of SHA256 (much faster)
2. Implements parallel processing with concurrent.futures
3. Adds a progress bar with tqdm
"""

import os
import pathlib
from typing import List, Optional, Tuple, Dict
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
import xxhash  # pip install xxhash
from tqdm import tqdm  # pip install tqdm

# Number of worker processes to use (adjust based on CPU cores)
NUM_WORKERS = os.cpu_count() or 4

def compute_file_fingerprint(file_path: pathlib.Path) -> Optional[str]:
    """
    Compute a fast fingerprint for a file using xxhash and metadata.
    xxhash is ~100x faster than SHA256 for this purpose.
    """
    try:
        # Get file metadata
        stat = file_path.stat()
        file_size = stat.st_size
        mtime = stat.st_mtime
        
        # Create a base fingerprint from metadata
        metadata = f"{file_path.name}|{file_size}|{mtime:.6f}"
        hasher = xxhash.xxh64()
        hasher.update(metadata.encode('utf-8'))
        
        # For very small files (< 16KB), include a content sample
        if file_size > 0 and file_size < 16384:
            with file_path.open('rb') as f:
                sample_size = min(file_size, 1024)
                content_sample = f.read(sample_size)
                hasher.update(content_sample)
        # For larger files, sample beginning, middle, and end
        elif file_size > 0:
            sample_size = 4096  # 4KB samples
            with file_path.open('rb') as f:
                # Read from beginning
                start_data = f.read(sample_size)
                hasher.update(start_data)
                
                # Read from middle if file is large enough
                if file_size > sample_size * 2:
                    middle_pos = file_size // 2 - sample_size // 2
                    f.seek(middle_pos)
                    middle_data = f.read(sample_size)
                    hasher.update(middle_data)
                
                # Read from end if file is large enough
                if file_size > sample_size * 2:
                    f.seek(-sample_size, 2)  # 2 means relative to end
                    end_data = f.read(sample_size)
                    hasher.update(end_data)
        
        # Get the hash as a hex string
        fingerprint = hasher.hexdigest()
        
        # Pad to 64 chars to match SHA256 length for compatibility
        fingerprint = fingerprint.ljust(64, '0')
        
        return fingerprint
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None

def process_file_batch(file_paths: List[pathlib.Path]) -> List[Tuple[pathlib.Path, str]]:
    """Process a batch of files and return their fingerprints."""
    results = []
    for path in file_paths:
        fingerprint = compute_file_fingerprint(path)
        if fingerprint:
            results.append((path, fingerprint))
    return results

def collect_file_fingerprints(
    root_dir: pathlib.Path,
    include_subdirs: bool = False,
    excluded_dirs: List[str] = None,
    excluded_patterns: List[str] = None
) -> Dict[str, pathlib.Path]:
    """
    Collect file fingerprints in parallel with progress bar.
    Returns a dictionary mapping fingerprints to file paths.
    """
    import fnmatch  # Import here to avoid circular imports
    
    excluded_dirs = set(excluded_dirs or [])
    excluded_patterns = excluded_patterns or []
    
    # Collect all file paths
    all_files = []
    
    # Use os.walk to gather files
    for root, dirs, files in os.walk(root_dir):
        # Remove excluded dirs from the walk
        dirs[:] = [d for d in dirs if d not in excluded_dirs]
        
        for filename in files:
            # Skip if matches any excluded pattern
            if any(fnmatch.fnmatch(filename, pat) for pat in excluded_patterns):
                continue
            
            file_path = pathlib.Path(root) / filename
            if file_path.is_file() and os.access(file_path, os.R_OK):
                all_files.append(file_path)
        
        if not include_subdirs:
            # If user doesn't want subdirectories, stop after top-level
            break
    
    # Create batches for parallel processing
    batch_size = max(1, len(all_files) // (NUM_WORKERS * 4))
    batches = [all_files[i:i + batch_size] for i in range(0, len(all_files), batch_size)]
    
    # Process batches in parallel with progress bar
    results = {}
    with ProcessPoolExecutor(max_workers=NUM_WORKERS) as executor:
        futures = [executor.submit(process_file_batch, batch) for batch in batches]
        
        with tqdm(total=len(all_files), desc="Processing files", unit="file") as pbar:
            for future in as_completed(futures):
                batch_results = future.result()
                for path, fingerprint in batch_results:
                    results[fingerprint] = path
                    pbar.update(1)
    
    return results

# Example usage:
if __name__ == "__main__":
    import time
    
    start_time = time.time()
    root_dir = pathlib.Path(".")
    fingerprints = collect_file_fingerprints(
        root_dir, 
        include_subdirs=True,
        excluded_dirs=[".git", "__pycache__", "venv"],
        excluded_patterns=["*.pyc", "*.pyo", "*.pyd"]
    )
    end_time = time.time()
    
    print(f"Processed {len(fingerprints)} files in {end_time - start_time:.2f} seconds")
    for fingerprint, path in list(fingerprints.items())[:5]:
        print(f"{path}: {fingerprint[:16]}...")